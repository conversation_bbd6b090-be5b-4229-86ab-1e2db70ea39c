<?php
require_once 'includes/session.php';

// Require login
requireLogin();

$page_title = 'Nạp thẻ - KAIO.PRO';
$error = '';
$success = '';

// Get card types
$db = new Database();
$db->query('SELECT * FROM card_types WHERE status = "active" ORDER BY name');
$card_types = $db->resultset();

// Card values
$card_values = [10000, 20000, 50000, 100000, 200000, 500000, 1000000];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $card_type = sanitizeInput($_POST['card_type']);
    $card_value = (int)$_POST['card_value'];
    $card_serial = sanitizeInput($_POST['card_serial']);
    $card_code = sanitizeInput($_POST['card_code']);

    if (empty($card_type) || empty($card_value) || empty($card_serial) || empty($card_code)) {
        $error = 'Vui lòng điền đầy đủ thông tin!';
    } elseif (!in_array($card_value, $card_values)) {
        $error = 'Mệnh giá thẻ không hợp lệ!';
    } else {
        // Get card type info
        $db->query('SELECT * FROM card_types WHERE code = :code AND status = "active"');
        $db->bind(':code', $card_type);
        $card_type_info = $db->single();

        if (!$card_type_info) {
            $error = 'Loại thẻ không hợp lệ!';
        } else {
            // Calculate final amount after discount
            $discount_rate = $card_type_info['discount_rate'];
            $final_amount = $card_value * (100 - $discount_rate) / 100;

            // Insert transaction
            $db->query('INSERT INTO transactions (user_id, type, amount, description, status) VALUES (:user_id, "nap_the", :amount, :description, "pending")');
            $db->bind(':user_id', $_SESSION['user_id']);
            $db->bind(':amount', $final_amount);
            $db->bind(':description', "Nạp thẻ {$card_type_info['name']} {$card_value}đ - Serial: {$card_serial}");

            if ($db->execute()) {
                $transaction_id = $db->lastInsertId();
                $success = "Gửi thẻ thành công! Mã giao dịch: #" . $transaction_id . ". Thẻ đang được xử lý. Số tiền nhận được: " . number_format($final_amount) . "đ";

                // In real implementation, you would integrate with card processing API here
                // For demo, we'll simulate processing

                // Clear form
                $_POST = [];
            } else {
                $error = 'Có lỗi xảy ra, vui lòng thử lại!';
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="form-container" style="max-width: 600px;">
    <h2 style="text-align: center; margin-bottom: 30px; color: #333;">
        <i class="fas fa-credit-card" style="margin-right: 10px; color: #f7931e;"></i>
        Nạp Thẻ Cào
    </h2>

    <?php if ($error): ?>
        <div class="flash-message flash-error">
            <?php echo $error; ?>
        </div>
    <?php endif; ?>

    <?php if ($success): ?>
        <div class="flash-message flash-success">
            <?php echo $success; ?>
        </div>
    <?php endif; ?>

    <form method="POST" action="">
        <div class="form-group">
            <label>Chọn loại thẻ:</label>
            <div class="card-types-grid">
                <?php foreach ($card_types as $type): ?>
                    <div class="card-type-item <?php echo (isset($_POST['card_type']) && $_POST['card_type'] == $type['code']) ? 'active' : ''; ?>"
                        data-type="<?php echo $type['code']; ?>">
                        <img src="assets/images/cards/<?php echo strtolower($type['code']); ?>.png"
                            alt="<?php echo $type['name']; ?>"
                            onerror="this.src='assets/images/cards/default.png'">
                        <span><?php echo $type['name']; ?></span>
                        <?php if ($type['discount_rate'] > 0): ?>
                            <small>-<?php echo $type['discount_rate']; ?>%</small>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
            <input type="hidden" id="card_type" name="card_type" value="<?php echo isset($_POST['card_type']) ? $_POST['card_type'] : ''; ?>" required>
        </div>

        <div class="form-group">
            <label>Chọn mệnh giá:</label>
            <div class="card-values-grid">
                <?php foreach ($card_values as $value): ?>
                    <div class="card-value-item <?php echo (isset($_POST['card_value']) && $_POST['card_value'] == $value) ? 'active' : ''; ?>"
                        data-value="<?php echo $value; ?>">
                        <?php echo number_format($value); ?>đ
                    </div>
                <?php endforeach; ?>
            </div>
            <input type="hidden" id="card_value" name="card_value" value="<?php echo isset($_POST['card_value']) ? $_POST['card_value'] : ''; ?>" required>
        </div>

        <div class="form-group">
            <label for="card_serial">Số serial:</label>
            <input type="text" id="card_serial" name="card_serial" class="form-control"
                value="<?php echo isset($_POST['card_serial']) ? htmlspecialchars($_POST['card_serial']) : ''; ?>"
                placeholder="Nhập số serial trên thẻ" required>
        </div>

        <div class="form-group">
            <label for="card_code">Mã thẻ:</label>
            <input type="text" id="card_code" name="card_code" class="form-control"
                value="<?php echo isset($_POST['card_code']) ? htmlspecialchars($_POST['card_code']) : ''; ?>"
                placeholder="Nhập mã thẻ (cào lớp bạc)" required>
        </div>

        <div class="form-group">
            <div class="info-box">
                <h4><i class="fas fa-info-circle"></i> Lưu ý quan trọng:</h4>
                <ul>
                    <li>Vui lòng kiểm tra kỹ thông tin trước khi gửi</li>
                    <li>Thẻ sai thông tin sẽ bị trừ 50% giá trị</li>
                    <li>Thời gian xử lý: 1-5 phút</li>
                    <li>Hỗ trợ 24/7 qua Telegram: @kaiopro</li>
                </ul>
            </div>
        </div>

        <div class="form-group">
            <button type="submit" class="btn-submit">
                <i class="fas fa-paper-plane"></i> Gửi Thẻ
            </button>
        </div>
    </form>

    <div style="text-align: center; margin-top: 20px;">
        <a href="transaction-history.php" style="color: #f7931e; text-decoration: none;">
            <i class="fas fa-history"></i> Xem lịch sử giao dịch
        </a>
    </div>
</div>

<style>
    .card-types-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin-bottom: 10px;
    }

    .card-type-item {
        border: 2px solid #ddd;
        border-radius: 10px;
        padding: 15px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
    }

    .card-type-item:hover {
        border-color: #f7931e;
        transform: translateY(-2px);
    }

    .card-type-item.active {
        border-color: #f7931e;
        background: rgba(247, 147, 30, 0.1);
    }

    .card-type-item img {
        width: 40px;
        height: 40px;
        margin-bottom: 8px;
    }

    .card-type-item span {
        display: block;
        font-weight: bold;
        font-size: 12px;
    }

    .card-type-item small {
        position: absolute;
        top: 5px;
        right: 5px;
        background: #dc3545;
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 10px;
    }

    .card-values-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
        margin-bottom: 10px;
    }

    .card-value-item {
        border: 2px solid #ddd;
        border-radius: 8px;
        padding: 12px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: bold;
    }

    .card-value-item:hover {
        border-color: #f7931e;
        background: rgba(247, 147, 30, 0.1);
    }

    .card-value-item.active {
        border-color: #f7931e;
        background: rgba(247, 147, 30, 0.2);
    }

    .info-box {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
    }

    .info-box h4 {
        color: #1976d2;
        margin-bottom: 10px;
    }

    .info-box ul {
        margin: 0;
        padding-left: 20px;
    }

    .info-box li {
        margin-bottom: 5px;
        color: #333;
    }

    @media (max-width: 768px) {
        .card-types-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }

        .card-values-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .card-type-item {
            padding: 10px;
        }

        .card-type-item img {
            width: 30px;
            height: 30px;
        }

        .card-type-item span {
            font-size: 11px;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Card type selection
        const cardTypeItems = document.querySelectorAll('.card-type-item');
        const cardTypeInput = document.getElementById('card_type');

        cardTypeItems.forEach(item => {
            item.addEventListener('click', function() {
                cardTypeItems.forEach(i => i.classList.remove('active'));
                this.classList.add('active');
                cardTypeInput.value = this.dataset.type;
            });
        });

        // Card value selection
        const cardValueItems = document.querySelectorAll('.card-value-item');
        const cardValueInput = document.getElementById('card_value');

        cardValueItems.forEach(item => {
            item.addEventListener('click', function() {
                cardValueItems.forEach(i => i.classList.remove('active'));
                this.classList.add('active');
                cardValueInput.value = this.dataset.value;
            });
        });
    });
</script>

<?php include 'includes/footer.php'; ?>