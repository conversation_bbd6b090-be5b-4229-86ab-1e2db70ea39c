<?php
require_once 'includes/session.php';
$page_title = 'KAIO.PRO - Trang chủ';
include 'includes/header.php';

// Get flash message
$flash = getFlashMessage();
?>

<?php if ($flash): ?>
    <div class="flash-message flash-<?php echo $flash['type']; ?>">
        <?php echo $flash['message']; ?>
    </div>
<?php endif; ?>

<!-- Menu Section -->
<div class="menu-section">
    <h2 class="section-title">Danh Sách Menu</h2>
    <div class="menu-grid">
        <a href="nap-the.php" class="menu-item">
            <i class="fas fa-credit-card menu-item-icon"></i>
            <div class="menu-item-title">Nạp Thẻ</div>
        </a>
        
        <a href="ban-bo-1-sao.php" class="menu-item">
            <i class="fas fa-star menu-item-icon"></i>
            <div class="menu-item-title"><PERSON><PERSON>ộ 1 Sao</div>
        </a>
        
        <a href="ban-vang.php" class="menu-item">
            <i class="fas fa-coins menu-item-icon"></i>
            <div class="menu-item-title">Bán Vàng</div>
        </a>
        
        <a href="ban-do-item.php" class="menu-item">
            <i class="fas fa-gem menu-item-icon"></i>
            <div class="menu-item-title">Bán Đồ Item</div>
        </a>
    </div>
</div>

<!-- Game Section -->
<div class="menu-section">
    <h2 class="section-title">Danh Mục Game</h2>
    <div class="game-grid">
        <a href="game.php?game=nro&type=nick-tam-trung" class="game-item">
            <i class="fas fa-dragon game-item-icon"></i>
            <div class="game-item-title">Nick Tầm Trung</div>
        </a>
        
        <a href="game.php?game=nro&type=nick-su-sinh" class="game-item">
            <i class="fas fa-user-ninja game-item-icon"></i>
            <div class="game-item-title">Nick Sư Sinh</div>
        </a>
        
        <a href="game.php?game=nro&type=nick-vang-gold" class="game-item">
            <i class="fas fa-crown game-item-icon"></i>
            <div class="game-item-title">Nick Vàng Gold</div>
        </a>
    </div>
</div>

<!-- Other Categories Section -->
<div class="menu-section">
    <h2 class="section-title">Danh Mục Khác</h2>
    <div class="game-grid">
        <a href="service.php?type=nap-vang" class="game-item">
            <i class="fas fa-fire game-item-icon"></i>
            <div class="game-item-title">Nạp Vàng<br>Vip Thần Linh</div>
        </a>
        
        <a href="service.php?type=nhap-do-item" class="game-item">
            <i class="fas fa-magic game-item-icon"></i>
            <div class="game-item-title">Nhập Đồ Item<br>Vip Thần Linh</div>
        </a>
        
        <a href="service.php?type=nhap-nick" class="game-item">
            <i class="fas fa-user-plus game-item-icon"></i>
            <div class="game-item-title">Nhập Nick<br>Vip Thần Linh</div>
        </a>
        
        <a href="service.php?type=rut-hub" class="game-item">
            <i class="fas fa-exchange-alt game-item-icon"></i>
            <div class="game-item-title">Rút Hub</div>
        </a>
        
        <a href="service.php?type=rut-vang" class="game-item">
            <i class="fas fa-hand-holding-usd game-item-icon"></i>
            <div class="game-item-title">Rút Vàng<br>Vip Thần Linh</div>
        </a>
        
        <a href="service.php?type=nhan-vang" class="game-item">
            <i class="fas fa-gift game-item-icon"></i>
            <div class="game-item-title">Nhận Vàng<br>Miễn Phí Thần Linh</div>
        </a>
    </div>
</div>

<!-- Statistics Section -->
<div class="menu-section">
    <h2 class="section-title">Thống Kê Hệ Thống</h2>
    <div class="stats-grid">
        <div class="stat-item">
            <i class="fas fa-users stat-icon"></i>
            <div class="stat-number">1,234</div>
            <div class="stat-label">Thành viên</div>
        </div>
        
        <div class="stat-item">
            <i class="fas fa-shopping-cart stat-icon"></i>
            <div class="stat-number">5,678</div>
            <div class="stat-label">Giao dịch</div>
        </div>
        
        <div class="stat-item">
            <i class="fas fa-dollar-sign stat-icon"></i>
            <div class="stat-number">999,999,999</div>
            <div class="stat-label">Tổng nạp (VNĐ)</div>
        </div>
        
        <div class="stat-item">
            <i class="fas fa-star stat-icon"></i>
            <div class="stat-number">4.9/5</div>
            <div class="stat-label">Đánh giá</div>
        </div>
    </div>
</div>

<!-- News Section -->
<div class="menu-section">
    <h2 class="section-title">Tin Tức & Thông Báo</h2>
    <div class="news-list">
        <div class="news-item">
            <div class="news-date">09/07/2024</div>
            <div class="news-title">🎉 Khuyến mãi nạp thẻ tháng 7 - Tặng 20% giá trị thẻ</div>
        </div>
        
        <div class="news-item">
            <div class="news-date">08/07/2024</div>
            <div class="news-title">⚡ Cập nhật server mới cho Ngọc Rồng Online</div>
        </div>
        
        <div class="news-item">
            <div class="news-date">07/07/2024</div>
            <div class="news-title">🔥 Event bán nick giá rẻ - Chỉ từ 50,000 VNĐ</div>
        </div>
    </div>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-item {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 36px;
    margin-bottom: 10px;
    display: block;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    opacity: 0.9;
}

.news-list {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.news-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #dee2e6;
}

.news-item:last-child {
    border-bottom: none;
}

.news-date {
    background: #007bff;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    margin-right: 15px;
    min-width: 80px;
    text-align: center;
}

.news-title {
    font-weight: bold;
    color: #333;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .stat-item {
        padding: 15px;
    }
    
    .stat-icon {
        font-size: 28px;
    }
    
    .stat-number {
        font-size: 20px;
    }
    
    .news-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .news-date {
        margin-right: 0;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
