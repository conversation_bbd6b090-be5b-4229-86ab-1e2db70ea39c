<?php
// Database setup script
require_once 'config/database.php';

echo "<h2>KAIO.PRO Database Setup</h2>";

try {
    // Read SQL file
    $sql = file_get_contents('database/setup.sql');
    
    if ($sql === false) {
        throw new Exception('Cannot read setup.sql file');
    }
    
    // Create database connection without specifying database name
    $dsn = 'mysql:host=' . DB_HOST . ';charset=utf8mb4';
    $pdo = new PDO($dsn, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "<p>Executing SQL statements...</p>";
    echo "<ul>";
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                
                // Extract table/database name for display
                if (preg_match('/CREATE\s+(DATABASE|TABLE)\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?/i', $statement, $matches)) {
                    echo "<li>✅ Created {$matches[1]}: {$matches[2]}</li>";
                } elseif (preg_match('/INSERT\s+INTO\s+`?(\w+)`?/i', $statement, $matches)) {
                    echo "<li>✅ Inserted data into: {$matches[1]}</li>";
                } else {
                    echo "<li>✅ Executed statement</li>";
                }
            } catch (PDOException $e) {
                echo "<li>❌ Error: " . $e->getMessage() . "</li>";
            }
        }
    }
    
    echo "</ul>";
    echo "<p><strong>✅ Database setup completed successfully!</strong></p>";
    echo "<p><a href='index.php'>Go to Homepage</a></p>";
    
} catch (Exception $e) {
    echo "<p><strong>❌ Error: " . $e->getMessage() . "</strong></p>";
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup - KAIO.PRO</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        h2 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        p {
            background: white;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        ul {
            background: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            list-style: none;
        }
        
        li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        li:last-child {
            border-bottom: none;
        }
        
        a {
            color: #f7931e;
            text-decoration: none;
            font-weight: bold;
        }
        
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
</html>
