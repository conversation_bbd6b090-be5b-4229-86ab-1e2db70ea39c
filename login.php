<?php
require_once 'includes/session.php';

// Redirect if already logged in
redirectIfLoggedIn();

$page_title = 'Đăng nhập - KAIO.PRO';
$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitizeInput($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error = 'Vui lòng nhập đầy đủ thông tin!';
    } else {
        $db = new Database();
        
        // Check if user exists
        $db->query('SELECT * FROM users WHERE (username = :username OR email = :username) AND status = "active"');
        $db->bind(':username', $username);
        $user = $db->single();
        
        if ($user && verifyPassword($password, $user['password'])) {
            // Login successful
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            
            setFlashMessage('success', 'Đăng nhập thành công!');
            header('Location: index.php');
            exit();
        } else {
            $error = 'Tên đăng nhập hoặc mật khẩu không đúng!';
        }
    }
}

include 'includes/header.php';
?>

<div class="form-container">
    <h2 style="text-align: center; margin-bottom: 30px; color: #333;">Đăng Nhập</h2>
    
    <?php if ($error): ?>
        <div class="flash-message flash-error">
            <?php echo $error; ?>
        </div>
    <?php endif; ?>
    
    <form method="POST" action="">
        <div class="form-group">
            <label for="username">Tên đăng nhập hoặc Email:</label>
            <input type="text" id="username" name="username" class="form-control" 
                   value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" 
                   required>
        </div>
        
        <div class="form-group">
            <label for="password">Mật khẩu:</label>
            <input type="password" id="password" name="password" class="form-control" required>
        </div>
        
        <div class="form-group">
            <button type="submit" class="btn-submit">Đăng Nhập</button>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <p>Chưa có tài khoản? <a href="register.php" style="color: #f7931e; font-weight: bold;">Đăng ký ngay</a></p>
            <p><a href="forgot-password.php" style="color: #666;">Quên mật khẩu?</a></p>
        </div>
    </form>
</div>

<style>
.form-container a {
    text-decoration: none;
    transition: color 0.3s ease;
}

.form-container a:hover {
    text-decoration: underline;
}

.form-group {
    position: relative;
}

.form-control:focus {
    box-shadow: 0 0 0 3px rgba(247, 147, 30, 0.1);
}

.btn-submit:active {
    transform: translateY(0);
}

@media (max-width: 480px) {
    .form-container {
        margin: 0 5px;
        padding: 20px 15px;
    }
    
    .form-container h2 {
        font-size: 20px;
        margin-bottom: 20px;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
