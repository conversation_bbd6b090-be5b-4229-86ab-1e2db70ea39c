<?php
require_once 'includes/session.php';
$page_title = 'Trang không tồn tại - KAIO.PRO';
http_response_code(404);
include 'includes/header.php';
?>

<div class="error-page">
    <div class="error-content">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h1>404</h1>
        <h2>Trang không tồn tại</h2>
        <p>Xin lỗi, trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển.</p>
        
        <div class="error-actions">
            <a href="index.php" class="btn btn-primary">
                <i class="fas fa-home"></i> Về trang chủ
            </a>
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
        
        <div class="search-suggestion">
            <p>Hoặc bạn có thể:</p>
            <ul>
                <li><a href="nap-the.php">Nạp thẻ cào</a></li>
                <li><a href="ban-vang.php">Bán vàng game</a></li>
                <li><a href="ban-do-item.php">Bán đồ item</a></li>
                <li><a href="transaction-history.php">Xem lịch sử giao dịch</a></li>
            </ul>
        </div>
    </div>
</div>

<style>
.error-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
    padding: 40px 20px;
}

.error-content {
    text-align: center;
    background: white;
    padding: 60px 40px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    max-width: 600px;
}

.error-icon {
    font-size: 80px;
    color: #f7931e;
    margin-bottom: 20px;
}

.error-content h1 {
    font-size: 120px;
    font-weight: bold;
    color: #333;
    margin: 0;
    line-height: 1;
}

.error-content h2 {
    font-size: 32px;
    color: #666;
    margin: 20px 0;
}

.error-content p {
    font-size: 18px;
    color: #888;
    margin-bottom: 40px;
    line-height: 1.6;
}

.error-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
}

.search-suggestion {
    border-top: 1px solid #eee;
    padding-top: 30px;
    margin-top: 30px;
}

.search-suggestion p {
    font-size: 16px;
    margin-bottom: 15px;
    color: #666;
}

.search-suggestion ul {
    list-style: none;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
}

.search-suggestion li a {
    color: #f7931e;
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s ease;
}

.search-suggestion li a:hover {
    color: #e67e22;
    text-decoration: underline;
}

@media (max-width: 768px) {
    .error-content {
        padding: 40px 20px;
    }
    
    .error-icon {
        font-size: 60px;
    }
    
    .error-content h1 {
        font-size: 80px;
    }
    
    .error-content h2 {
        font-size: 24px;
    }
    
    .error-content p {
        font-size: 16px;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .search-suggestion ul {
        flex-direction: column;
        gap: 10px;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
