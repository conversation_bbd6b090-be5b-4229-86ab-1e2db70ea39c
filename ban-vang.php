<?php
require_once 'includes/session.php';

// Require login
requireLogin();

$page_title = 'Bán vàng - KAIO.PRO';
$error = '';
$success = '';

// Get games
$db = new Database();
$db->query('SELECT * FROM games WHERE status = "active" ORDER BY name');
$games = $db->resultset();

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $game_id = (int)$_POST['game_id'];
    $server_id = (int)$_POST['server_id'];
    $character_name = sanitizeInput($_POST['character_name']);
    $gold_amount = (int)$_POST['gold_amount'];
    $price_per_unit = (int)$_POST['price_per_unit'];
    $total_price = $gold_amount * $price_per_unit;
    
    if (empty($game_id) || empty($server_id) || empty($character_name) || empty($gold_amount) || empty($price_per_unit)) {
        $error = 'Vui lòng điền đầy đủ thông tin!';
    } elseif ($gold_amount < 1000000) {
        $error = 'Số lượng vàng tối thiểu là 1,000,000!';
    } elseif ($price_per_unit < 100) {
        $error = 'Giá bán tối thiểu là 100đ/1 triệu vàng!';
    } else {
        // Verify game and server
        $db->query('SELECT g.name as game_name, s.server_name FROM games g 
                   JOIN game_servers s ON g.id = s.game_id 
                   WHERE g.id = :game_id AND s.id = :server_id AND g.status = "active"');
        $db->bind(':game_id', $game_id);
        $db->bind(':server_id', $server_id);
        $game_server = $db->single();
        
        if (!$game_server) {
            $error = 'Game hoặc server không hợp lệ!';
        } else {
            // Insert transaction
            $description = "Bán vàng {$game_server['game_name']} - {$game_server['server_name']} - Nhân vật: {$character_name} - Số lượng: " . number_format($gold_amount) . " vàng";
            
            $db->query('INSERT INTO transactions (user_id, type, amount, description, status, game_id, server_id, character_name) 
                       VALUES (:user_id, "ban_vang", :amount, :description, "pending", :game_id, :server_id, :character_name)');
            $db->bind(':user_id', $_SESSION['user_id']);
            $db->bind(':amount', $total_price);
            $db->bind(':description', $description);
            $db->bind(':game_id', $game_id);
            $db->bind(':server_id', $server_id);
            $db->bind(':character_name', $character_name);
            
            if ($db->execute()) {
                $transaction_id = $db->lastInsertId();
                $success = "Đăng bán vàng thành công! Mã giao dịch: #" . $transaction_id . ". Tổng giá trị: " . number_format($total_price) . "đ";
                
                // Clear form
                $_POST = [];
            } else {
                $error = 'Có lỗi xảy ra, vui lòng thử lại!';
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="form-container" style="max-width: 600px;">
    <h2 style="text-align: center; margin-bottom: 30px; color: #333;">
        <i class="fas fa-coins" style="margin-right: 10px; color: #f7931e;"></i>
        Bán Vàng Game
    </h2>
    
    <?php if ($error): ?>
        <div class="flash-message flash-error">
            <?php echo $error; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($success): ?>
        <div class="flash-message flash-success">
            <?php echo $success; ?>
        </div>
    <?php endif; ?>
    
    <form method="POST" action="">
        <div class="form-group">
            <label for="game_id">Chọn game:</label>
            <select id="game_id" name="game_id" class="form-control" required>
                <option value="">Chọn game...</option>
                <?php foreach ($games as $game): ?>
                    <option value="<?php echo $game['id']; ?>" 
                            <?php echo (isset($_POST['game_id']) && $_POST['game_id'] == $game['id']) ? 'selected' : ''; ?>>
                        <?php echo $game['name']; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="form-group">
            <label for="server_id">Chọn máy chủ:</label>
            <select id="server_id" name="server_id" class="form-control" required>
                <option value="">Chọn máy chủ...</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="character_name">Tên nhân vật:</label>
            <input type="text" id="character_name" name="character_name" class="form-control" 
                   value="<?php echo isset($_POST['character_name']) ? htmlspecialchars($_POST['character_name']) : ''; ?>" 
                   placeholder="Nhập tên nhân vật trong game" required>
        </div>
        
        <div class="form-group">
            <label for="gold_amount">Số lượng vàng (triệu):</label>
            <input type="number" id="gold_amount" name="gold_amount" class="form-control" 
                   value="<?php echo isset($_POST['gold_amount']) ? $_POST['gold_amount'] : ''; ?>" 
                   placeholder="Ví dụ: 1000000 (1 triệu vàng)" min="1000000" step="1000000" required>
            <small style="color: #666; font-size: 12px;">Tối thiểu 1,000,000 vàng</small>
        </div>
        
        <div class="form-group">
            <label for="price_per_unit">Giá bán (VNĐ/1 triệu vàng):</label>
            <input type="number" id="price_per_unit" name="price_per_unit" class="form-control" 
                   value="<?php echo isset($_POST['price_per_unit']) ? $_POST['price_per_unit'] : ''; ?>" 
                   placeholder="Ví dụ: 1000 (1000đ/1 triệu vàng)" min="100" step="100" required>
            <small style="color: #666; font-size: 12px;">Tối thiểu 100đ/1 triệu vàng</small>
        </div>
        
        <div class="form-group">
            <div class="price-calculator">
                <h4>Tính toán giá:</h4>
                <div class="calc-row">
                    <span>Số lượng vàng:</span>
                    <span id="calc-gold">0</span>
                </div>
                <div class="calc-row">
                    <span>Giá/1 triệu:</span>
                    <span id="calc-price">0đ</span>
                </div>
                <div class="calc-row total">
                    <span>Tổng tiền:</span>
                    <span id="calc-total">0đ</span>
                </div>
            </div>
        </div>
        
        <div class="form-group">
            <div class="info-box">
                <h4><i class="fas fa-info-circle"></i> Lưu ý quan trọng:</h4>
                <ul>
                    <li>Vàng phải có sẵn trong tài khoản game</li>
                    <li>Tên nhân vật phải chính xác</li>
                    <li>Thời gian xử lý: 5-30 phút</li>
                    <li>Phí giao dịch: 5% tổng giá trị</li>
                    <li>Hỗ trợ 24/7 qua Telegram: @kaiopro</li>
                </ul>
            </div>
        </div>
        
        <div class="form-group">
            <button type="submit" class="btn-submit">
                <i class="fas fa-handshake"></i> Đăng Bán Vàng
            </button>
        </div>
    </form>
    
    <div style="text-align: center; margin-top: 20px;">
        <a href="transaction-history.php" style="color: #f7931e; text-decoration: none;">
            <i class="fas fa-history"></i> Xem lịch sử giao dịch
        </a>
    </div>
</div>

<style>
.price-calculator {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.price-calculator h4 {
    margin-bottom: 15px;
    color: #333;
}

.calc-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 5px 0;
}

.calc-row.total {
    border-top: 2px solid #f7931e;
    margin-top: 10px;
    padding-top: 10px;
    font-weight: bold;
    font-size: 18px;
    color: #f7931e;
}

.info-box {
    background: #fff3cd;
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.info-box h4 {
    color: #856404;
    margin-bottom: 10px;
}

.info-box ul {
    margin: 0;
    padding-left: 20px;
}

.info-box li {
    margin-bottom: 5px;
    color: #333;
}

@media (max-width: 768px) {
    .calc-row {
        font-size: 14px;
    }
    
    .calc-row.total {
        font-size: 16px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const gameSelect = document.getElementById('game_id');
    const serverSelect = document.getElementById('server_id');
    const goldAmountInput = document.getElementById('gold_amount');
    const pricePerUnitInput = document.getElementById('price_per_unit');
    
    // Load servers when game changes
    gameSelect.addEventListener('change', function() {
        const gameId = this.value;
        serverSelect.innerHTML = '<option value="">Chọn máy chủ...</option>';
        
        if (gameId) {
            fetch(`api/get-servers.php?game_id=${gameId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        data.servers.forEach(server => {
                            const option = document.createElement('option');
                            option.value = server.id;
                            option.textContent = server.server_name;
                            serverSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error:', error));
        }
    });
    
    // Calculate total price
    function updateCalculation() {
        const goldAmount = parseInt(goldAmountInput.value) || 0;
        const pricePerUnit = parseInt(pricePerUnitInput.value) || 0;
        const total = goldAmount * pricePerUnit;
        
        document.getElementById('calc-gold').textContent = goldAmount.toLocaleString('vi-VN');
        document.getElementById('calc-price').textContent = pricePerUnit.toLocaleString('vi-VN') + 'đ';
        document.getElementById('calc-total').textContent = total.toLocaleString('vi-VN') + 'đ';
    }
    
    goldAmountInput.addEventListener('input', updateCalculation);
    pricePerUnitInput.addEventListener('input', updateCalculation);
    
    // Initial calculation
    updateCalculation();
});
</script>

<?php include 'includes/footer.php'; ?>
