<?php
require_once 'includes/session.php';

// Require login
requireLogin();

$page_title = '<PERSON><PERSON> đồ item - KAIO.PRO';
$error = '';
$success = '';

// Get games
$db = new Database();
$db->query('SELECT * FROM games WHERE status = "active" ORDER BY name');
$games = $db->resultset();

// Item categories
$item_categories = [
    'weapon' => 'Vũ khí',
    'armor' => 'Áo giáp',
    'accessory' => 'Phụ kiện',
    'consumable' => 'Vật phẩm tiêu hao',
    'material' => 'Nguyên liệu',
    'other' => 'Khác'
];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $game_id = (int)$_POST['game_id'];
    $server_id = (int)$_POST['server_id'];
    $character_name = sanitizeInput($_POST['character_name']);
    $item_name = sanitizeInput($_POST['item_name']);
    $item_category = sanitizeInput($_POST['item_category']);
    $item_description = sanitizeInput($_POST['item_description']);
    $item_price = (int)$_POST['item_price'];
    
    if (empty($game_id) || empty($server_id) || empty($character_name) || empty($item_name) || empty($item_category) || empty($item_price)) {
        $error = 'Vui lòng điền đầy đủ thông tin bắt buộc!';
    } elseif ($item_price < 10000) {
        $error = 'Giá bán tối thiểu là 10,000đ!';
    } elseif (!array_key_exists($item_category, $item_categories)) {
        $error = 'Danh mục item không hợp lệ!';
    } else {
        // Verify game and server
        $db->query('SELECT g.name as game_name, s.server_name FROM games g 
                   JOIN game_servers s ON g.id = s.game_id 
                   WHERE g.id = :game_id AND s.id = :server_id AND g.status = "active"');
        $db->bind(':game_id', $game_id);
        $db->bind(':server_id', $server_id);
        $game_server = $db->single();
        
        if (!$game_server) {
            $error = 'Game hoặc server không hợp lệ!';
        } else {
            // Insert item
            $db->query('INSERT INTO items (game_id, name, description, price, category, status) 
                       VALUES (:game_id, :name, :description, :price, :category, "available")');
            $db->bind(':game_id', $game_id);
            $db->bind(':name', $item_name);
            $db->bind(':description', $item_description);
            $db->bind(':price', $item_price);
            $db->bind(':category', $item_category);
            
            if ($db->execute()) {
                $item_id = $db->lastInsertId();
                
                // Insert transaction
                $description = "Bán item {$game_server['game_name']} - {$game_server['server_name']} - Nhân vật: {$character_name} - Item: {$item_name}";
                
                $db->query('INSERT INTO transactions (user_id, type, amount, description, status, game_id, server_id, character_name) 
                           VALUES (:user_id, "ban_item", :amount, :description, "pending", :game_id, :server_id, :character_name)');
                $db->bind(':user_id', $_SESSION['user_id']);
                $db->bind(':amount', $item_price);
                $db->bind(':description', $description);
                $db->bind(':game_id', $game_id);
                $db->bind(':server_id', $server_id);
                $db->bind(':character_name', $character_name);
                
                if ($db->execute()) {
                    $transaction_id = $db->lastInsertId();
                    $success = "Đăng bán item thành công! Mã giao dịch: #" . $transaction_id . ". Giá bán: " . number_format($item_price) . "đ";
                    
                    // Clear form
                    $_POST = [];
                } else {
                    $error = 'Có lỗi xảy ra khi tạo giao dịch!';
                }
            } else {
                $error = 'Có lỗi xảy ra khi đăng item!';
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="form-container" style="max-width: 600px;">
    <h2 style="text-align: center; margin-bottom: 30px; color: #333;">
        <i class="fas fa-gem" style="margin-right: 10px; color: #f7931e;"></i>
        Bán Đồ Item
    </h2>
    
    <?php if ($error): ?>
        <div class="flash-message flash-error">
            <?php echo $error; ?>
        </div>
    <?php endif; ?>
    
    <?php if ($success): ?>
        <div class="flash-message flash-success">
            <?php echo $success; ?>
        </div>
    <?php endif; ?>
    
    <form method="POST" action="">
        <div class="form-group">
            <label for="game_id">Chọn game: <span style="color: red;">*</span></label>
            <select id="game_id" name="game_id" class="form-control" required>
                <option value="">Chọn game...</option>
                <?php foreach ($games as $game): ?>
                    <option value="<?php echo $game['id']; ?>" 
                            <?php echo (isset($_POST['game_id']) && $_POST['game_id'] == $game['id']) ? 'selected' : ''; ?>>
                        <?php echo $game['name']; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="form-group">
            <label for="server_id">Chọn máy chủ: <span style="color: red;">*</span></label>
            <select id="server_id" name="server_id" class="form-control" required>
                <option value="">Chọn máy chủ...</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="character_name">Tên nhân vật: <span style="color: red;">*</span></label>
            <input type="text" id="character_name" name="character_name" class="form-control" 
                   value="<?php echo isset($_POST['character_name']) ? htmlspecialchars($_POST['character_name']) : ''; ?>" 
                   placeholder="Nhập tên nhân vật sở hữu item" required>
        </div>
        
        <div class="form-group">
            <label for="item_name">Tên item: <span style="color: red;">*</span></label>
            <input type="text" id="item_name" name="item_name" class="form-control" 
                   value="<?php echo isset($_POST['item_name']) ? htmlspecialchars($_POST['item_name']) : ''; ?>" 
                   placeholder="Ví dụ: Kiếm Rồng +15" required>
        </div>
        
        <div class="form-group">
            <label for="item_category">Danh mục item: <span style="color: red;">*</span></label>
            <select id="item_category" name="item_category" class="form-control" required>
                <option value="">Chọn danh mục...</option>
                <?php foreach ($item_categories as $key => $value): ?>
                    <option value="<?php echo $key; ?>" 
                            <?php echo (isset($_POST['item_category']) && $_POST['item_category'] == $key) ? 'selected' : ''; ?>>
                        <?php echo $value; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="form-group">
            <label for="item_description">Mô tả item:</label>
            <textarea id="item_description" name="item_description" class="form-control" rows="4" 
                      placeholder="Mô tả chi tiết về item (chỉ số, tính năng đặc biệt...)"><?php echo isset($_POST['item_description']) ? htmlspecialchars($_POST['item_description']) : ''; ?></textarea>
        </div>
        
        <div class="form-group">
            <label for="item_price">Giá bán (VNĐ): <span style="color: red;">*</span></label>
            <input type="number" id="item_price" name="item_price" class="form-control" 
                   value="<?php echo isset($_POST['item_price']) ? $_POST['item_price'] : ''; ?>" 
                   placeholder="Ví dụ: 500000" min="10000" step="1000" required>
            <small style="color: #666; font-size: 12px;">Tối thiểu 10,000đ</small>
        </div>
        
        <div class="form-group">
            <div class="info-box">
                <h4><i class="fas fa-info-circle"></i> Lưu ý quan trọng:</h4>
                <ul>
                    <li>Item phải có sẵn trong tài khoản game</li>
                    <li>Tên nhân vật và item phải chính xác</li>
                    <li>Mô tả chi tiết giúp bán nhanh hơn</li>
                    <li>Phí giao dịch: 10% tổng giá trị</li>
                    <li>Thời gian xử lý: 10-60 phút</li>
                    <li>Hỗ trợ 24/7 qua Telegram: @kaiopro</li>
                </ul>
            </div>
        </div>
        
        <div class="form-group">
            <button type="submit" class="btn-submit">
                <i class="fas fa-store"></i> Đăng Bán Item
            </button>
        </div>
    </form>
    
    <div style="text-align: center; margin-top: 20px;">
        <a href="transaction-history.php" style="color: #f7931e; text-decoration: none; margin-right: 20px;">
            <i class="fas fa-history"></i> Lịch sử giao dịch
        </a>
        <a href="marketplace.php" style="color: #f7931e; text-decoration: none;">
            <i class="fas fa-shopping-cart"></i> Xem chợ item
        </a>
    </div>
</div>

<style>
.info-box {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.info-box h4 {
    color: #0c5460;
    margin-bottom: 10px;
}

.info-box ul {
    margin: 0;
    padding-left: 20px;
}

.info-box li {
    margin-bottom: 5px;
    color: #333;
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

@media (max-width: 768px) {
    .form-container {
        margin: 0 10px;
    }
    
    textarea.form-control {
        min-height: 80px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const gameSelect = document.getElementById('game_id');
    const serverSelect = document.getElementById('server_id');
    const priceInput = document.getElementById('item_price');
    
    // Load servers when game changes
    gameSelect.addEventListener('change', function() {
        const gameId = this.value;
        serverSelect.innerHTML = '<option value="">Chọn máy chủ...</option>';
        
        if (gameId) {
            fetch(`api/get-servers.php?game_id=${gameId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        data.servers.forEach(server => {
                            const option = document.createElement('option');
                            option.value = server.id;
                            option.textContent = server.server_name;
                            serverSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error('Error:', error));
        }
    });
    
    // Format price input
    priceInput.addEventListener('input', function() {
        let value = this.value.replace(/[^\d]/g, '');
        if (value) {
            this.value = parseInt(value);
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
