-- Create database
CREATE DATABASE IF NOT EXISTS kaio_pro_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE kaio_pro_db;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    phone VARCHAR(20),
    balance DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Games table
CREATE TABLE IF NOT EXISTS games (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    image VARCHAR(255),
    category ENUM('nro', 'other') DEFAULT 'other',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Game servers table
CREATE TABLE IF NOT EXISTS game_servers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    game_id INT,
    server_name VARCHAR(50) NOT NULL,
    server_code VARCHAR(20) NOT NULL,
    status ENUM('active', 'maintenance', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE
);

-- Transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    type ENUM('nap_the', 'ban_vang', 'ban_item', 'mua_item') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    description TEXT,
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    game_id INT,
    server_id INT,
    character_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE SET NULL,
    FOREIGN KEY (server_id) REFERENCES game_servers(id) ON DELETE SET NULL
);

-- Items table
CREATE TABLE IF NOT EXISTS items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    game_id INT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    image VARCHAR(255),
    price DECIMAL(15,2) NOT NULL,
    category VARCHAR(50),
    status ENUM('available', 'sold', 'inactive') DEFAULT 'available',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE
);

-- Card types table
CREATE TABLE IF NOT EXISTS card_types (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    discount_rate DECIMAL(5,2) DEFAULT 0.00,
    status ENUM('active', 'inactive') DEFAULT 'active'
);

-- Insert sample data
INSERT INTO games (name, slug, description, image, category) VALUES
('Ngọc Rồng Online', 'nro', 'Game Ngọc Rồng Online phiên bản chính thức', 'nro.jpg', 'nro'),
('Ninja School Online', 'nso', 'Game Ninja School Online', 'nso.jpg', 'other'),
('Làng Lá Phiêu Lưu Ký', 'llplk', 'Game Làng Lá Phiêu Lưu Ký', 'llplk.jpg', 'other');

INSERT INTO game_servers (game_id, server_name, server_code) VALUES
(1, 'Sao Thần', 'st'),
(1, 'Sao Địa', 'sd'),
(1, 'Sao Nhân', 'sn'),
(2, 'Máy chủ 1', 'mc1'),
(2, 'Máy chủ 2', 'mc2'),
(3, 'Server Alpha', 'alpha'),
(3, 'Server Beta', 'beta');

INSERT INTO card_types (name, code, discount_rate) VALUES
('Viettel', 'VTT', 0.00),
('Mobifone', 'VMS', 0.00),
('Vinaphone', 'VNP', 0.00),
('Vietnamobile', 'VNMB', 5.00),
('Gmobile', 'GMOBILE', 5.00);

-- Create admin user (password: admin123)
INSERT INTO users (username, email, password, full_name, status) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'active');
