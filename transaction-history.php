<?php
require_once 'includes/session.php';

// Require login
requireLogin();

$page_title = 'Lịch sử giao dịch - KAIO.PRO';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Get transactions
$db = new Database();
$db->query('SELECT COUNT(*) as total FROM transactions WHERE user_id = :user_id');
$db->bind(':user_id', $_SESSION['user_id']);
$total_result = $db->single();
$total_records = $total_result['total'];
$total_pages = ceil($total_records / $limit);

$db->query('SELECT t.*, g.name as game_name, s.server_name 
           FROM transactions t 
           LEFT JOIN games g ON t.game_id = g.id 
           LEFT JOIN game_servers s ON t.server_id = s.id 
           WHERE t.user_id = :user_id 
           ORDER BY t.created_at DESC 
           LIMIT :limit OFFSET :offset');
$db->bind(':user_id', $_SESSION['user_id']);
$db->bind(':limit', $limit);
$db->bind(':offset', $offset);
$transactions = $db->resultset();

include 'includes/header.php';
?>

<div class="container" style="max-width: 1000px;">
    <h2 style="text-align: center; margin-bottom: 30px; color: #333;">
        <i class="fas fa-history" style="margin-right: 10px; color: #f7931e;"></i>
        Lịch Sử Giao Dịch
    </h2>
    
    <?php if (empty($transactions)): ?>
        <div class="empty-state">
            <i class="fas fa-inbox" style="font-size: 64px; color: #ccc; margin-bottom: 20px;"></i>
            <h3>Chưa có giao dịch nào</h3>
            <p>Bạn chưa thực hiện giao dịch nào. Hãy bắt đầu với việc nạp thẻ hoặc bán vàng!</p>
            <div style="margin-top: 20px;">
                <a href="nap-the.php" class="btn btn-primary" style="margin-right: 10px;">Nạp thẻ</a>
                <a href="ban-vang.php" class="btn btn-secondary">Bán vàng</a>
            </div>
        </div>
    <?php else: ?>
        <div class="transactions-container">
            <?php foreach ($transactions as $transaction): ?>
                <div class="transaction-item">
                    <div class="transaction-header">
                        <div class="transaction-id">
                            <strong>#<?php echo $transaction['id']; ?></strong>
                            <span class="transaction-type type-<?php echo $transaction['type']; ?>">
                                <?php
                                switch($transaction['type']) {
                                    case 'nap_the': echo 'Nạp thẻ'; break;
                                    case 'ban_vang': echo 'Bán vàng'; break;
                                    case 'ban_item': echo 'Bán item'; break;
                                    case 'mua_item': echo 'Mua item'; break;
                                    default: echo ucfirst($transaction['type']);
                                }
                                ?>
                            </span>
                        </div>
                        <div class="transaction-status">
                            <span class="status status-<?php echo $transaction['status']; ?>">
                                <?php
                                switch($transaction['status']) {
                                    case 'pending': echo 'Đang xử lý'; break;
                                    case 'completed': echo 'Hoàn thành'; break;
                                    case 'failed': echo 'Thất bại'; break;
                                    case 'cancelled': echo 'Đã hủy'; break;
                                    default: echo ucfirst($transaction['status']);
                                }
                                ?>
                            </span>
                        </div>
                    </div>
                    
                    <div class="transaction-body">
                        <div class="transaction-description">
                            <?php echo htmlspecialchars($transaction['description']); ?>
                        </div>
                        
                        <?php if ($transaction['game_name']): ?>
                            <div class="transaction-game">
                                <i class="fas fa-gamepad"></i>
                                <?php echo $transaction['game_name']; ?>
                                <?php if ($transaction['server_name']): ?>
                                    - <?php echo $transaction['server_name']; ?>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($transaction['character_name']): ?>
                            <div class="transaction-character">
                                <i class="fas fa-user"></i>
                                Nhân vật: <?php echo htmlspecialchars($transaction['character_name']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="transaction-footer">
                        <div class="transaction-amount">
                            <strong><?php echo number_format($transaction['amount']); ?>đ</strong>
                        </div>
                        <div class="transaction-date">
                            <?php echo date('d/m/Y H:i', strtotime($transaction['created_at'])); ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                    <a href="?page=<?php echo $page - 1; ?>" class="page-btn">
                        <i class="fas fa-chevron-left"></i> Trước
                    </a>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <a href="?page=<?php echo $i; ?>" 
                       class="page-btn <?php echo $i == $page ? 'active' : ''; ?>">
                        <?php echo $i; ?>
                    </a>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <a href="?page=<?php echo $page + 1; ?>" class="page-btn">
                        Sau <i class="fas fa-chevron-right"></i>
                    </a>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<style>
.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.empty-state h3 {
    color: #666;
    margin-bottom: 10px;
}

.empty-state p {
    color: #999;
    margin-bottom: 20px;
}

.transactions-container {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.transaction-item {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    margin-bottom: 15px;
    padding: 20px;
    transition: all 0.3s ease;
}

.transaction-item:hover {
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.transaction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f1f3f4;
}

.transaction-id {
    display: flex;
    align-items: center;
    gap: 10px;
}

.transaction-type {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.type-nap_the {
    background: #e3f2fd;
    color: #1976d2;
}

.type-ban_vang {
    background: #fff3e0;
    color: #f57c00;
}

.type-ban_item {
    background: #f3e5f5;
    color: #7b1fa2;
}

.type-mua_item {
    background: #e8f5e8;
    color: #388e3c;
}

.status {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-completed {
    background: #d4edda;
    color: #155724;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}

.status-cancelled {
    background: #e2e3e5;
    color: #383d41;
}

.transaction-body {
    margin-bottom: 15px;
}

.transaction-description {
    color: #333;
    margin-bottom: 8px;
    line-height: 1.5;
}

.transaction-game,
.transaction-character {
    color: #666;
    font-size: 14px;
    margin-bottom: 5px;
}

.transaction-game i,
.transaction-character i {
    margin-right: 5px;
    color: #f7931e;
}

.transaction-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 10px;
    border-top: 1px solid #f1f3f4;
}

.transaction-amount {
    font-size: 18px;
    color: #f7931e;
}

.transaction-date {
    color: #666;
    font-size: 14px;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 30px;
}

.page-btn {
    padding: 8px 12px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.page-btn:hover {
    background: #f7931e;
    color: white;
    border-color: #f7931e;
}

.page-btn.active {
    background: #f7931e;
    color: white;
    border-color: #f7931e;
}

@media (max-width: 768px) {
    .transaction-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .transaction-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .transaction-amount {
        font-size: 16px;
    }
    
    .pagination {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .page-btn {
        padding: 6px 10px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .transactions-container {
        padding: 15px;
    }
    
    .transaction-item {
        padding: 15px;
    }
    
    .transaction-type,
    .status {
        font-size: 11px;
        padding: 3px 6px;
    }
    
    .transaction-description {
        font-size: 14px;
    }
    
    .transaction-game,
    .transaction-character {
        font-size: 13px;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
