<?php
// Simple test file
echo "<h1>PHP Test</h1>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current time: " . date('Y-m-d H:i:s') . "</p>";

// Check required extensions
echo "<h2>Extensions</h2>";
$extensions = ['pdo', 'pdo_mysql', 'session'];
foreach ($extensions as $ext) {
    $status = extension_loaded($ext) ? '✅ Loaded' : '❌ Missing';
    echo "<p>{$ext}: {$status}</p>";
}

// Test session
echo "<h3>Session Test</h3>";
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
echo "<p>Session Status: " . (session_status() == PHP_SESSION_ACTIVE ? '✅ Active' : '❌ Inactive') . "</p>";

// Test database connection (without creating database)
echo "<h3>Database Connection Test</h3>";
try {
    $dsn = 'mysql:host=localhost;charset=utf8mb4';
    $pdo = new PDO($dsn, 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p>MySQL Connection: ✅ Success</p>";

    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE 'kaio_pro_db'");
    $db_exists = $stmt->rowCount() > 0;
    echo "<p>Database 'kaio_pro_db': " . ($db_exists ? '✅ Exists' : '❌ Not found') . "</p>";

    if (!$db_exists) {
        echo "<p><strong>⚠️ Database not found. Please run <a href='setup.php'>setup.php</a> first.</strong></p>";
    }
} catch (PDOException $e) {
    echo "<p>MySQL Connection: ❌ Failed - " . $e->getMessage() . "</p>";
}

// Test file permissions
echo "<h3>File Permissions Test</h3>";
$files_to_check = [
    'config/database.php',
    'includes/session.php',
    'includes/header.php',
    'assets/css/style.css'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $readable = is_readable($file) ? '✅' : '❌';
        echo "<p>{$file}: {$readable} " . (is_readable($file) ? 'Readable' : 'Not readable') . "</p>";
    } else {
        echo "<p>{$file}: ❌ File not found</p>";
    }
}

// Test error reporting
echo "<h3>Error Reporting</h3>";
echo "<p>Error Reporting Level: " . error_reporting() . "</p>";
echo "<p>Display Errors: " . (ini_get('display_errors') ? 'On' : 'Off') . "</p>";

// Show PHP errors if any
if (function_exists('error_get_last')) {
    $last_error = error_get_last();
    if ($last_error) {
        echo "<h3>Last PHP Error</h3>";
        echo "<pre>" . print_r($last_error, true) . "</pre>";
    }
}

echo "<hr>";
echo "<p><a href='setup.php'>Run Database Setup</a> | <a href='index.php'>Go to Homepage</a></p>";
?>

<!DOCTYPE html>
<html>

<head>
    <title>KAIO.PRO System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }

        h2,
        h3 {
            color: #333;
        }

        p {
            margin: 5px 0;
        }

        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }

        a {
            color: #f7931e;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }
    </style>
</head>

</html>