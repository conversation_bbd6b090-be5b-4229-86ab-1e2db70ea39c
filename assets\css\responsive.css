/* Responsive Design */

/* Tablet styles */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-content {
        flex-wrap: wrap;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .auth-buttons {
        display: none;
        width: 100%;
        justify-content: center;
        margin-top: 15px;
        flex-wrap: wrap;
    }
    
    .auth-buttons.active {
        display: flex;
    }
    
    .menu-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .game-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 10px;
    }
    
    .footer-content {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }
    
    .section-title {
        font-size: 20px;
    }
    
    .menu-item {
        padding: 15px;
    }
    
    .menu-item-icon {
        font-size: 36px;
    }
    
    .menu-item-title {
        font-size: 14px;
    }
    
    .game-item-icon {
        font-size: 28px;
    }
    
    .game-item-title {
        font-size: 11px;
    }
}

/* Mobile styles */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }
    
    .header {
        padding: 10px 0;
    }
    
    .logo-text {
        font-size: 20px;
    }
    
    .logo-img {
        width: 30px;
        height: 30px;
    }
    
    .menu-section {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .menu-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
    
    .game-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
    }
    
    .menu-item {
        padding: 12px;
    }
    
    .menu-item-icon {
        font-size: 28px;
        margin-bottom: 8px;
    }
    
    .menu-item-title {
        font-size: 12px;
    }
    
    .game-item {
        padding: 10px;
    }
    
    .game-item-icon {
        font-size: 24px;
        margin-bottom: 5px;
    }
    
    .game-item-title {
        font-size: 10px;
    }
    
    .section-title {
        font-size: 18px;
        margin-bottom: 15px;
    }
    
    .footer {
        padding: 30px 0 15px;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .footer-section {
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    .form-container {
        padding: 20px;
        margin: 0 10px;
    }
    
    .btn {
        padding: 6px 12px;
        font-size: 14px;
    }
    
    .auth-buttons {
        gap: 8px;
    }
    
    .welcome-text {
        font-size: 14px;
        margin-bottom: 10px;
        width: 100%;
        text-align: center;
    }
}

/* Extra small mobile */
@media (max-width: 320px) {
    .menu-grid {
        grid-template-columns: 1fr;
    }
    
    .game-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .logo-text {
        font-size: 18px;
    }
    
    .section-title {
        font-size: 16px;
    }
    
    .menu-item-title {
        font-size: 11px;
    }
    
    .game-item-title {
        font-size: 9px;
    }
}

/* Landscape mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .header {
        padding: 8px 0;
    }
    
    .main-content {
        padding: 15px 0;
    }
    
    .menu-section {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .section-title {
        font-size: 18px;
        margin-bottom: 15px;
    }
    
    .footer {
        padding: 20px 0 10px;
    }
}

/* Print styles */
@media print {
    .header,
    .footer,
    .auth-buttons,
    .mobile-menu-toggle {
        display: none;
    }
    
    body {
        background: white;
        color: black;
    }
    
    .menu-section {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .menu-item,
    .game-item {
        background: white;
        color: black;
        border: 1px solid #ddd;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo-img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .menu-section {
        background: #2c3e50;
        color: white;
    }
    
    .section-title {
        color: #f39c12;
    }
    
    .form-container {
        background: #34495e;
        color: white;
    }
    
    .form-control {
        background: #2c3e50;
        color: white;
        border-color: #555;
    }
    
    .form-group label {
        color: white;
    }
}
