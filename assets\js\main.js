// Main JavaScript file for KAIO.PRO

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
    const authButtons = document.querySelector('.auth-buttons');
    
    if (mobileMenuToggle && authButtons) {
        mobileMenuToggle.addEventListener('click', function() {
            authButtons.classList.toggle('active');
        });
    }
    
    // Auto-hide flash messages
    const flashMessages = document.querySelectorAll('.flash-message');
    flashMessages.forEach(function(message) {
        setTimeout(function() {
            message.style.opacity = '0';
            setTimeout(function() {
                message.remove();
            }, 300);
        }, 5000);
    });
    
    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    isValid = false;
                    field.style.borderColor = '#dc3545';
                    
                    // Remove error styling on input
                    field.addEventListener('input', function() {
                        this.style.borderColor = '#ddd';
                    });
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showNotification('Vui lòng điền đầy đủ thông tin bắt buộc!', 'error');
            }
        });
    });
    
    // Card selection functionality
    const cardItems = document.querySelectorAll('.card-item');
    cardItems.forEach(function(item) {
        item.addEventListener('click', function() {
            // Remove active class from all items
            cardItems.forEach(function(card) {
                card.classList.remove('active');
            });
            
            // Add active class to clicked item
            this.classList.add('active');
            
            // Update hidden input if exists
            const cardTypeInput = document.querySelector('#card_type');
            if (cardTypeInput) {
                cardTypeInput.value = this.dataset.type;
            }
            
            const cardValueInput = document.querySelector('#card_value');
            if (cardValueInput) {
                cardValueInput.value = this.dataset.value;
            }
        });
    });
    
    // Server selection functionality
    const serverSelect = document.querySelector('#server_id');
    const gameSelect = document.querySelector('#game_id');
    
    if (gameSelect && serverSelect) {
        gameSelect.addEventListener('change', function() {
            const gameId = this.value;
            
            // Clear server options
            serverSelect.innerHTML = '<option value="">Chọn máy chủ...</option>';
            
            if (gameId) {
                // Fetch servers for selected game
                fetch(`api/get-servers.php?game_id=${gameId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            data.servers.forEach(function(server) {
                                const option = document.createElement('option');
                                option.value = server.id;
                                option.textContent = server.server_name;
                                serverSelect.appendChild(option);
                            });
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching servers:', error);
                    });
            }
        });
    }
    
    // Copy to clipboard functionality
    const copyButtons = document.querySelectorAll('.copy-btn');
    copyButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const target = document.querySelector(this.dataset.target);
            if (target) {
                target.select();
                document.execCommand('copy');
                showNotification('Đã sao chép vào clipboard!', 'success');
            }
        });
    });
    
    // Loading overlay
    function showLoading() {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Đang xử lý...</p>
            </div>
        `;
        document.body.appendChild(overlay);
    }
    
    function hideLoading() {
        const overlay = document.querySelector('.loading-overlay');
        if (overlay) {
            overlay.remove();
        }
    }
    
    // AJAX form submission
    const ajaxForms = document.querySelectorAll('.ajax-form');
    ajaxForms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            showLoading();
            
            const formData = new FormData(this);
            
            fetch(this.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                
                if (data.success) {
                    showNotification(data.message, 'success');
                    if (data.redirect) {
                        setTimeout(() => {
                            window.location.href = data.redirect;
                        }, 1500);
                    }
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                hideLoading();
                showNotification('Có lỗi xảy ra, vui lòng thử lại!', 'error');
                console.error('Error:', error);
            });
        });
    });
    
    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
            <button class="notification-close">&times;</button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.remove();
        }, 5000);
        
        // Manual close
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });
    }
    
    function getNotificationIcon(type) {
        switch(type) {
            case 'success': return 'check-circle';
            case 'error': return 'exclamation-circle';
            case 'warning': return 'exclamation-triangle';
            default: return 'info-circle';
        }
    }
    
    // Make functions globally available
    window.showNotification = showNotification;
    window.showLoading = showLoading;
    window.hideLoading = hideLoading;
    
    // Price formatting
    function formatPrice(price) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    }
    
    // Update price displays
    const priceElements = document.querySelectorAll('.price');
    priceElements.forEach(function(element) {
        const price = parseInt(element.textContent);
        if (!isNaN(price)) {
            element.textContent = formatPrice(price);
        }
    });
    
    // Lazy loading for images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
});

// Additional CSS for notifications and loading
const additionalCSS = `
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    color: #333;
}

.loading-spinner i {
    font-size: 36px;
    margin-bottom: 15px;
    color: #f7931e;
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 1000;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.notification-success {
    border-left: 4px solid #28a745;
    color: #155724;
}

.notification-error {
    border-left: 4px solid #dc3545;
    color: #721c24;
}

.notification-warning {
    border-left: 4px solid #ffc107;
    color: #856404;
}

.notification-info {
    border-left: 4px solid #17a2b8;
    color: #0c5460;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    margin-left: auto;
    opacity: 0.7;
}

.notification-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.card-item.active {
    border: 3px solid #f7931e;
    transform: scale(1.05);
}

@media (max-width: 768px) {
    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
    }
}
`;

// Inject additional CSS
const style = document.createElement('style');
style.textContent = additionalCSS;
document.head.appendChild(style);
