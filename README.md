# KAIO.PRO - Website Game Online

Website hoàn chỉnh cho dịch vụ game online với các tính năng nạp thẻ, bán và<PERSON>, bán đồ item và quản lý giao dịch.

## 🚀 Tính năng chính

- **Hệ thống đăng nhập/đăng ký** với xác thực bảo mật
- **Nạp thẻ cào** hỗ trợ nhiều nhà mạng (Viettel, Mobifone, Vinaphone...)
- **Bán vàng game** với tính toán giá tự động
- **Bán đồ item** với phân loại chi tiết
- **Lịch sử giao dịch** với phân trang
- **Responsive design** tối ưu cho mobile
- **Dashboard quản trị** (đang phát triển)

## 🛠️ Công nghệ sử dụng

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Icons**: Font Awesome 6
- **Responsive**: CSS Grid & Flexbox

## 📋 Yêu cầu hệ thống

- PHP 7.4 hoặc cao hơn
- MySQL 5.7 hoặc cao hơn
- Apache/Nginx web server
- Extension PHP: PDO, PDO_MySQL

## 🔧 Cài đặt

### 1. Clone/Download dự án
```bash
git clone [repository-url]
cd WEBSITENRO
```

### 2. Cấu hình database
Chỉnh sửa file `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_NAME', 'kaio_pro_db');
```

### 3. Thiết lập database
Truy cập: `http://your-domain/setup.php`

Hoặc import thủ công file `database/setup.sql` vào MySQL.

### 4. Cấu hình web server
Đảm bảo document root trỏ đến thư mục dự án và mod_rewrite được bật.

## 👤 Tài khoản mặc định

- **Username**: admin
- **Password**: admin123
- **Email**: <EMAIL>

## 📁 Cấu trúc thư mục

```
WEBSITENRO/
├── api/                    # API endpoints
├── assets/                 # CSS, JS, Images
│   ├── css/
│   ├── js/
│   └── images/
├── config/                 # Cấu hình
├── database/              # SQL files
├── includes/              # Header, Footer, Session
├── index.php              # Trang chủ
├── login.php              # Đăng nhập
├── register.php           # Đăng ký
├── nap-the.php           # Nạp thẻ
├── ban-vang.php          # Bán vàng
├── ban-do-item.php       # Bán đồ item
├── transaction-history.php # Lịch sử giao dịch
└── setup.php             # Thiết lập database
```

## 🎮 Hướng dẫn sử dụng

### Đối với người dùng:
1. Đăng ký tài khoản mới
2. Đăng nhập vào hệ thống
3. Sử dụng các tính năng:
   - Nạp thẻ cào để có tiền trong tài khoản
   - Bán vàng game để kiếm tiền
   - Bán đồ item để trao đổi
   - Xem lịch sử giao dịch

### Đối với admin:
1. Đăng nhập bằng tài khoản admin
2. Quản lý người dùng và giao dịch
3. Cấu hình hệ thống

## 🔒 Bảo mật

- Mật khẩu được hash bằng PHP password_hash()
- Prepared statements chống SQL injection
- Input validation và sanitization
- Session management bảo mật

## 📱 Responsive Design

Website được tối ưu cho:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (320px - 767px)

## 🚧 Tính năng sắp tới

- [ ] Dashboard admin hoàn chỉnh
- [ ] Tích hợp API nạp thẻ thật
- [ ] Hệ thống thông báo real-time
- [ ] Chat support
- [ ] Báo cáo thống kê
- [ ] API mobile app

## 🐛 Báo lỗi

Nếu gặp lỗi, vui lòng tạo issue với thông tin:
- Mô tả lỗi chi tiết
- Các bước tái hiện
- Screenshot (nếu có)
- Thông tin môi trường (PHP version, MySQL version...)

## 📄 License

Dự án này được phát hành dưới MIT License.

## 👨‍💻 Tác giả

Được phát triển bởi AI Assistant với yêu cầu từ người dùng.

## 🤝 Đóng góp

Mọi đóng góp đều được chào đón! Vui lòng:
1. Fork dự án
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Tạo Pull Request

---

**Lưu ý**: Đây là phiên bản demo. Trong môi trường production, cần bổ sung thêm các biện pháp bảo mật và tối ưu hiệu suất.
