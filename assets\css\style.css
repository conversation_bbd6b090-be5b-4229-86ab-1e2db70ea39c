/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header styles */
.header {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
}

.logo-img {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.logo-text {
    font-size: 24px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.auth-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-primary {
    background: #28a745;
    color: white;
}

.btn-primary:hover {
    background: #218838;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #dc3545;
    color: white;
}

.btn-secondary:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.divider {
    color: white;
    font-weight: bold;
}

.welcome-text {
    color: white;
    font-weight: bold;
}

.mobile-menu-toggle {
    display: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
}

/* Main content */
.main-content {
    min-height: calc(100vh - 200px);
    padding: 20px 0;
}

/* Menu categories */
.menu-section {
    background: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.section-title {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.menu-item {
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.menu-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.menu-item:hover::before {
    left: 100%;
}

.menu-item-icon {
    font-size: 48px;
    margin-bottom: 10px;
    display: block;
}

.menu-item-title {
    font-size: 16px;
    font-weight: bold;
    text-transform: uppercase;
}

/* Game grid */
.game-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.game-item {
    background: linear-gradient(45deg, #8B0000, #DC143C);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    position: relative;
}

.game-item:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.game-item-icon {
    font-size: 36px;
    margin-bottom: 8px;
    display: block;
}

.game-item-title {
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 40px 0 20px;
    margin-top: 50px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h3 {
    margin-bottom: 15px;
    color: #f39c12;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #f39c12;
}

.contact-info p {
    margin-bottom: 8px;
    color: #bdc3c7;
}

.contact-info i {
    margin-right: 8px;
    color: #f39c12;
}

.social-links {
    display: flex;
    gap: 10px;
}

.social-link {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: #34495e;
    color: white;
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #f39c12;
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Flash messages */
.flash-message {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
    font-weight: bold;
}

.flash-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.flash-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.flash-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Form styles */
.form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    max-width: 500px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #f7931e;
}

.btn-submit {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}
