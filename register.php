<?php
require_once 'includes/session.php';

// Redirect if already logged in
redirectIfLoggedIn();

$page_title = 'Đăng ký - KAIO.PRO';
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitizeInput($_POST['username']);
    $email = sanitizeInput($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $full_name = sanitizeInput($_POST['full_name']);
    $phone = sanitizeInput($_POST['phone']);
    
    // Validation
    if (empty($username) || empty($email) || empty($password) || empty($confirm_password)) {
        $error = 'Vui lòng điền đầy đủ thông tin bắt buộc!';
    } elseif (strlen($username) < 3 || strlen($username) > 20) {
        $error = 'Tên đăng nhập phải từ 3-20 ký tự!';
    } elseif (!validateEmail($email)) {
        $error = 'Email không hợp lệ!';
    } elseif (strlen($password) < 6) {
        $error = 'Mật khẩu phải có ít nhất 6 ký tự!';
    } elseif ($password !== $confirm_password) {
        $error = 'Mật khẩu xác nhận không khớp!';
    } else {
        $db = new Database();
        
        // Check if username exists
        $db->query('SELECT id FROM users WHERE username = :username');
        $db->bind(':username', $username);
        if ($db->single()) {
            $error = 'Tên đăng nhập đã tồn tại!';
        } else {
            // Check if email exists
            $db->query('SELECT id FROM users WHERE email = :email');
            $db->bind(':email', $email);
            if ($db->single()) {
                $error = 'Email đã được sử dụng!';
            } else {
                // Create new user
                $hashed_password = hashPassword($password);
                
                $db->query('INSERT INTO users (username, email, password, full_name, phone) VALUES (:username, :email, :password, :full_name, :phone)');
                $db->bind(':username', $username);
                $db->bind(':email', $email);
                $db->bind(':password', $hashed_password);
                $db->bind(':full_name', $full_name);
                $db->bind(':phone', $phone);
                
                if ($db->execute()) {
                    setFlashMessage('success', 'Đăng ký thành công! Vui lòng đăng nhập.');
                    header('Location: login.php');
                    exit();
                } else {
                    $error = 'Có lỗi xảy ra, vui lòng thử lại!';
                }
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="form-container">
    <h2 style="text-align: center; margin-bottom: 30px; color: #333;">Đăng Ký Tài Khoản</h2>
    
    <?php if ($error): ?>
        <div class="flash-message flash-error">
            <?php echo $error; ?>
        </div>
    <?php endif; ?>
    
    <form method="POST" action="">
        <div class="form-group">
            <label for="username">Tên đăng nhập: <span style="color: red;">*</span></label>
            <input type="text" id="username" name="username" class="form-control" 
                   value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" 
                   required minlength="3" maxlength="20">
            <small style="color: #666; font-size: 12px;">3-20 ký tự, chỉ chứa chữ cái, số và dấu gạch dưới</small>
        </div>
        
        <div class="form-group">
            <label for="email">Email: <span style="color: red;">*</span></label>
            <input type="email" id="email" name="email" class="form-control" 
                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                   required>
        </div>
        
        <div class="form-group">
            <label for="full_name">Họ và tên:</label>
            <input type="text" id="full_name" name="full_name" class="form-control" 
                   value="<?php echo isset($_POST['full_name']) ? htmlspecialchars($_POST['full_name']) : ''; ?>">
        </div>
        
        <div class="form-group">
            <label for="phone">Số điện thoại:</label>
            <input type="tel" id="phone" name="phone" class="form-control" 
                   value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
        </div>
        
        <div class="form-group">
            <label for="password">Mật khẩu: <span style="color: red;">*</span></label>
            <input type="password" id="password" name="password" class="form-control" required minlength="6">
            <small style="color: #666; font-size: 12px;">Ít nhất 6 ký tự</small>
        </div>
        
        <div class="form-group">
            <label for="confirm_password">Xác nhận mật khẩu: <span style="color: red;">*</span></label>
            <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" required style="margin-right: 8px;">
                Tôi đồng ý với <a href="terms.php" target="_blank" style="color: #f7931e;">Điều khoản sử dụng</a> 
                và <a href="privacy.php" target="_blank" style="color: #f7931e;">Chính sách bảo mật</a>
            </label>
        </div>
        
        <div class="form-group">
            <button type="submit" class="btn-submit">Đăng Ký</button>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <p>Đã có tài khoản? <a href="login.php" style="color: #f7931e; font-weight: bold;">Đăng nhập ngay</a></p>
        </div>
    </form>
</div>

<style>
.form-container small {
    display: block;
    margin-top: 5px;
}

.form-group label {
    display: flex;
    align-items: center;
}

.form-group label input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

.form-container a {
    text-decoration: none;
    transition: color 0.3s ease;
}

.form-container a:hover {
    text-decoration: underline;
}

@media (max-width: 480px) {
    .form-container {
        margin: 0 5px;
        padding: 20px 15px;
    }
    
    .form-container h2 {
        font-size: 20px;
        margin-bottom: 20px;
    }
    
    .form-group small {
        font-size: 11px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePassword() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Mật khẩu xác nhận không khớp');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    password.addEventListener('change', validatePassword);
    confirmPassword.addEventListener('keyup', validatePassword);
    
    // Username validation
    const username = document.getElementById('username');
    username.addEventListener('input', function() {
        const value = this.value;
        const regex = /^[a-zA-Z0-9_]+$/;
        
        if (value && !regex.test(value)) {
            this.setCustomValidity('Tên đăng nhập chỉ được chứa chữ cái, số và dấu gạch dưới');
        } else {
            this.setCustomValidity('');
        }
    });
});
</script>

<?php include 'includes/footer.php'; ?>
