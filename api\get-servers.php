<?php
header('Content-Type: application/json');
require_once '../config/database.php';

if (!isset($_GET['game_id']) || empty($_GET['game_id'])) {
    echo json_encode(['success' => false, 'message' => 'Game ID is required']);
    exit;
}

$game_id = (int)$_GET['game_id'];

try {
    $db = new Database();
    $db->query('SELECT id, server_name, server_code FROM game_servers WHERE game_id = :game_id AND status = "active" ORDER BY server_name');
    $db->bind(':game_id', $game_id);
    $servers = $db->resultset();
    
    echo json_encode([
        'success' => true,
        'servers' => $servers
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error'
    ]);
}
?>
